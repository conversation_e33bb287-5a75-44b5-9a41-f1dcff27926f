// Exam Practice Application
class ExamApp {
    constructor() {
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.examMode = 'practice'; // 'practice' or 'exam'
        this.isExamFinished = false;
        
        this.initializeApp();
        this.loadDefaultQuestions();
    }

    initializeApp() {
        this.bindEvents();
        this.loadThemePreference();
        this.showScreen('start-screen');
    }

    bindEvents() {
        // Mode selection
        document.querySelectorAll('.mode-card').forEach(card => {
            card.addEventListener('click', () => this.selectMode(card));
        });

        // Home button
        document.getElementById('home-btn').addEventListener('click', () => {
            this.goHome();
        });

        // Start exam
        document.getElementById('start-exam').addEventListener('click', () => {
            this.startExam();
        });

        // View answers
        document.getElementById('view-answers').addEventListener('click', () => {
            this.showAnswers();
        });

        // Back to start from answers
        document.getElementById('back-to-start').addEventListener('click', () => {
            this.goHome();
        });

        // Question navigation
        document.getElementById('prev-question').addEventListener('click', () => {
            this.navigateQuestion(-1);
        });

        document.getElementById('next-question').addEventListener('click', () => {
            this.navigateQuestion(1);
        });

        document.getElementById('submit-answer').addEventListener('click', () => {
            this.submitAnswer();
        });

        document.getElementById('submit-exam').addEventListener('click', () => {
            this.submitExam();
        });

        document.getElementById('finish-exam').addEventListener('click', () => {
            this.finishExam();
        });

        // Results actions
        document.getElementById('review-answers').addEventListener('click', () => {
            this.showReview();
        });

        document.getElementById('restart-exam').addEventListener('click', () => {
            this.restartExam();
        });

        // Modal close
        document.getElementById('close-error').addEventListener('click', () => {
            this.hideModal('error-modal');
        });

        // Score popup handlers
        document.getElementById('close-score-popup').addEventListener('click', () => {
            this.hideModal('score-popup');
        });

        document.getElementById('view-detailed-results').addEventListener('click', () => {
            this.hideModal('score-popup');
            this.showResults();
        });

        // Option selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.option')) {
                this.selectOption(e.target.closest('.option'));
            }
        });

        // Answer search and filter
        document.getElementById('answer-search').addEventListener('input', (e) => {
            this.filterAnswers();
        });

        document.getElementById('answer-filter').addEventListener('change', (e) => {
            this.filterAnswers();
        });

        // Theme selection
        document.getElementById('theme-select').addEventListener('change', (e) => {
            this.changeTheme(e.target.value);
        });

        // Practice mode controls
        document.getElementById('show-answer').addEventListener('click', () => {
            this.showCorrectAnswer();
        });

        document.getElementById('hide-answer').addEventListener('click', () => {
            this.hideCorrectAnswer();
        });

        document.getElementById('clear-answer').addEventListener('click', () => {
            this.clearCurrentAnswer();
        });

        document.getElementById('submit-practice').addEventListener('click', () => {
            this.finishExam();
        });
    }

    selectMode(selectedCard) {
        document.querySelectorAll('.mode-card').forEach(card => {
            card.classList.remove('selected');
        });
        selectedCard.classList.add('selected');

        this.examMode = selectedCard.dataset.mode;
        document.getElementById('start-exam').disabled = false;

        // Update mode display
        const modeDisplay = this.examMode === 'practice' ? 'Practice Mode' : 'Real Exam Mode';
        document.getElementById('exam-mode-display').textContent = modeDisplay;

        // Show/hide randomization options
        const randomizationOptions = document.getElementById('randomization-options');
        if (this.examMode === 'practice') {
            // Show options for practice mode
            randomizationOptions.classList.remove('hidden');
        } else {
            // Hide options for real exam mode (always randomized)
            randomizationOptions.classList.add('hidden');
            // Set default randomization for real exam
            document.getElementById('randomize-questions').checked = true;
            document.getElementById('randomize-answers').checked = true;
        }
    }

    changeTheme(theme) {
        // Remove existing theme classes
        document.body.classList.remove('theme-gradient', 'theme-dark', 'theme-light');

        // Add new theme class
        document.body.classList.add(`theme-${theme}`);

        // Save theme preference
        localStorage.setItem('examTheme', theme);

        console.log(`Theme changed to: ${theme}`);
    }

    loadThemePreference() {
        // Load saved theme or default to gradient
        const savedTheme = localStorage.getItem('examTheme') || 'gradient';

        // Set the dropdown
        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.value = savedTheme;
        }

        // Apply the theme
        this.changeTheme(savedTheme);
    }

    goHome() {
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.isExamFinished = false;

        // Reset questions to original state
        this.loadDefaultQuestions();

        // Reset displays
        document.getElementById('score-display').textContent = 'Score: 0/0';
        document.getElementById('exam-mode-display').textContent = 'Practice Mode';
        document.getElementById('progress-display').textContent = 'Question 1 of 60';

        // Reset mode selection
        document.querySelectorAll('.mode-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.getElementById('start-exam').disabled = true;

        // Hide randomization options
        document.getElementById('randomization-options').classList.add('hidden');

        // Hide home button and show start screen
        document.getElementById('home-btn').classList.add('hidden');
        this.showScreen('start-screen');
    }



    loadDefaultQuestions() {
        // Load questions from the embedded questions.js file
        this.loadEmbeddedQuestions();
    }

    shuffleArray(array) {
        // Fisher-Yates shuffle algorithm
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    randomizeQuestions() {
        if (document.getElementById('randomize-questions').checked) {
            this.questions = this.shuffleArray(this.questions);
            console.log('Questions randomized');
        }
    }

    randomizeAnswerChoices() {
        if (document.getElementById('randomize-answers').checked) {
            this.questions = this.questions.map(question => {
                // Create a mapping of original indices to shuffled indices
                const originalOptions = [...question.options];
                const shuffledOptions = this.shuffleArray(originalOptions);

                // Create mapping from old letters to new letters
                const letterMapping = {};
                originalOptions.forEach((option, originalIndex) => {
                    const originalLetter = option.charAt(0);
                    const newIndex = shuffledOptions.findIndex(opt => opt === option);
                    const newLetter = String.fromCharCode(65 + newIndex); // A, B, C, D...
                    letterMapping[originalLetter] = newLetter;
                });

                // Update the correct answer based on the new mapping
                const newCorrectAnswer = question.correctAnswer
                    .split('')
                    .map(letter => letterMapping[letter])
                    .sort()
                    .join('');

                // Update option letters to match new positions
                const updatedOptions = shuffledOptions.map((option, index) => {
                    const newLetter = String.fromCharCode(65 + index);
                    return newLetter + option.substring(1);
                });

                return {
                    ...question,
                    options: updatedOptions,
                    correctAnswer: newCorrectAnswer
                };
            });
            console.log('Answer choices randomized');
        }
    }

    loadEmbeddedQuestions() {
        // Use questions from the external questions.js file if available
        if (typeof window !== 'undefined' && window.examQuestions) {
            this.questions = window.examQuestions;
            console.log(`Loaded ${this.questions.length} questions from questions.js`);
            return;
        }

        // Fallback embedded questions if external file not available
        this.questions = [
            {
                id: "Question #1",
                text: "What is a symptom of an issue with vSphere HA?",
                options: [
                    "A. Snapshots cannot be created or consolidated.",
                    "B. VMs are not restarted after a host failure.",
                    "C. VMs are not being migrated using vMotion.",
                    "D. Hosts frequently disconnect from vCenter Server."
                ],
                correctAnswer: "B",
                explanation: "vSphere HA is responsible for restarting VMs after host failures."
            },
            {
                id: "Question #2",
                text: "Which prerequisite task must be completed before deploying VMware Cloud Foundation (VCF) using VMware Cloud Builder?",
                options: [
                    "A. Ensure that all ESXi hosts are connected to a single vCenter Server.",
                    "B. Verify network configurations and DNS settings for all components.",
                    "C. Verify configurations on Virtual Distributed Switch on the ESXi hosts that will be used for the deployment.",
                    "D. Ensure HCX is configured for workload migration."
                ],
                correctAnswer: "B",
                explanation: "Network configurations and DNS settings are critical prerequisites for VCF deployment."
            }
        ];

        console.log(`Loaded ${this.questions.length} fallback embedded questions`);
    }

    startExam() {
        if (this.questions.length === 0) {
            this.showError('No questions available.');
            return;
        }

        // Apply randomization based on settings
        this.randomizeAnswerChoices(); // Do this first to maintain question integrity
        this.randomizeQuestions();     // Then randomize question order

        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.isExamFinished = false;

        // Show home button
        document.getElementById('home-btn').classList.remove('hidden');

        this.updateProgress();
        this.showScreen('question-screen');
        this.displayQuestion();
    }

    displayQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        if (!question) return;

        // Update question text
        document.getElementById('question-text').textContent = question.text;

        // Update question number
        document.getElementById('current-question').textContent = this.currentQuestionIndex + 1;
        document.getElementById('total-questions').textContent = this.questions.length;

        // Add selection counter for multiple choice questions
        this.updateSelectionCounter();

        // Create options
        const optionsContainer = document.getElementById('question-options');
        optionsContainer.innerHTML = '';

        question.options.forEach((option, index) => {
            const optionElement = this.createOptionElement(option, index);
            optionsContainer.appendChild(optionElement);
        });

        // Hide feedback section initially
        document.getElementById('feedback-section').classList.add('hidden');

        // Reset practice controls
        this.resetPracticeControls();

        // Show mode-specific controls
        if (this.examMode === 'practice') {
            document.getElementById('practice-controls').classList.remove('hidden');
            document.getElementById('exam-controls').classList.remove('hidden');
            // Hide exam-only buttons in practice mode
            document.getElementById('submit-exam').style.display = 'none';
            document.getElementById('finish-exam').style.display = this.currentQuestionIndex === this.questions.length - 1 ? 'inline-block' : 'none';
            // Show submit practice button in practice mode
            document.getElementById('submit-practice').style.display = 'inline-block';
        } else {
            document.getElementById('practice-controls').classList.add('hidden');
            document.getElementById('exam-controls').classList.remove('hidden');
            // Hide practice-only buttons in exam mode
            document.getElementById('finish-exam').style.display = 'none';
            document.getElementById('submit-exam').style.display = this.currentQuestionIndex === this.questions.length - 1 ? 'inline-block' : 'none';
            // Hide submit practice button in real exam mode
            document.getElementById('submit-practice').style.display = 'none';
        }

        // Show previous answer if exists
        const userAnswer = this.userAnswers[this.currentQuestionIndex];
        if (userAnswer !== undefined) {
            this.selectOptionByIndex(userAnswer);

            // Show feedback in practice mode
            if (this.examMode === 'practice') {
                this.showFeedback();
            }
        }

        // Update navigation buttons and progress
        this.updateNavigationButtons();
        this.updateProgress();
    }

    createOptionElement(optionText, index) {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'option';
        optionDiv.dataset.index = index;
        
        const letter = optionText.charAt(0);
        const text = optionText.substring(2).trim();
        
        optionDiv.innerHTML = `
            <span class="option-letter">${letter}</span>
            <span class="option-text">${text}</span>
        `;
        
        return optionDiv;
    }

    selectOption(optionElement) {
        const question = this.questions[this.currentQuestionIndex];
        const answerIndex = parseInt(optionElement.dataset.index);

        // Clear any previous feedback styling
        document.querySelectorAll('.option').forEach(opt => {
            opt.classList.remove('correct', 'incorrect');
        });

        if (question.multipleChoice) {
            // Handle multiple choice questions - allow toggle
            const isCurrentlySelected = optionElement.classList.contains('selected');

            if (isCurrentlySelected) {
                // Deselect if already selected
                optionElement.classList.remove('selected');
            } else {
                // Select if not selected
                optionElement.classList.add('selected');
            }

            // Get all selected options
            const selectedOptions = Array.from(document.querySelectorAll('.option.selected'))
                .map(opt => parseInt(opt.dataset.index));

            if (selectedOptions.length > 0) {
                this.userAnswers[this.currentQuestionIndex] = selectedOptions;
            } else {
                // Remove answer if no options selected
                delete this.userAnswers[this.currentQuestionIndex];
            }
        } else {
            // Handle single choice questions - allow deselection
            const isCurrentlySelected = optionElement.classList.contains('selected');

            // Clear all selections first
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });

            if (!isCurrentlySelected) {
                // Select if it wasn't selected before
                optionElement.classList.add('selected');
                this.userAnswers[this.currentQuestionIndex] = answerIndex;
            } else {
                // Remove answer if deselecting
                delete this.userAnswers[this.currentQuestionIndex];
            }
        }

        // Hide feedback section when changing answers
        document.getElementById('feedback-section').classList.add('hidden');

        // Show feedback immediately in practice mode (only if answer is selected)
        if (this.examMode === 'practice' && this.userAnswers[this.currentQuestionIndex] !== undefined) {
            // For multiple choice questions, only show feedback if user has selected the expected number of answers
            if (question.multipleChoice) {
                const expectedAnswerCount = this.getExpectedAnswerCount(question);
                const userSelectedCount = Array.isArray(this.userAnswers[this.currentQuestionIndex])
                    ? this.userAnswers[this.currentQuestionIndex].length
                    : 0;

                // Only show feedback if user has selected the expected number of answers
                if (userSelectedCount === expectedAnswerCount) {
                    this.showFeedback();
                }
            } else {
                // For single choice questions, show feedback immediately
                this.showFeedback();
            }
        }

        // Update navigation buttons and score display
        this.updateNavigationButtons();
        this.updateScoreDisplay();
        this.updateSelectionCounter();
    }

    updateSelectionCounter() {
        const question = this.questions[this.currentQuestionIndex];
        const selectionCounterElement = document.getElementById('selection-counter');

        if (!selectionCounterElement) {
            // Create the selection counter element if it doesn't exist
            const counterDiv = document.createElement('div');
            counterDiv.id = 'selection-counter';
            counterDiv.className = 'selection-counter';

            // Insert after question text
            const questionText = document.getElementById('question-text');
            questionText.parentNode.insertBefore(counterDiv, questionText.nextSibling);
        }

        if (question.multipleChoice) {
            const expectedCount = this.getExpectedAnswerCount(question);
            const userAnswer = this.userAnswers[this.currentQuestionIndex];
            const selectedCount = Array.isArray(userAnswer) ? userAnswer.length : 0;

            const counterElement = document.getElementById('selection-counter');
            counterElement.innerHTML = `
                <div class="selection-info">
                    <span class="selection-text">Selected: ${selectedCount} of ${expectedCount} required</span>
                    ${selectedCount === expectedCount ? '<span class="selection-complete">✓ Complete</span>' : ''}
                </div>
            `;
            counterElement.style.display = 'block';
        } else {
            // Hide counter for single choice questions
            const counterElement = document.getElementById('selection-counter');
            if (counterElement) {
                counterElement.style.display = 'none';
            }
        }
    }

    getExpectedAnswerCount(question) {
        // Parse the question text to determine how many answers are expected
        const text = question.text.toLowerCase();

        // Look for patterns like "choose two", "choose three", "choose four", etc.
        const chooseMatch = text.match(/choose\s+(two|three|four|five|six|seven|eight|nine|ten|\d+)/);
        if (chooseMatch) {
            const numberWord = chooseMatch[1];
            const numberMap = {
                'two': 2, 'three': 3, 'four': 4, 'five': 5, 'six': 6,
                'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10
            };

            // If it's a word, convert to number
            if (numberMap[numberWord]) {
                return numberMap[numberWord];
            }

            // If it's already a number, parse it
            const num = parseInt(numberWord);
            if (!isNaN(num)) {
                return num;
            }
        }

        // Fallback: use the length of the correct answer
        return question.correctAnswer ? question.correctAnswer.length : 2;
    }

    selectOptionByIndex(answerData) {
        const question = this.questions[this.currentQuestionIndex];
        const options = document.querySelectorAll('.option');

        if (question.multipleChoice && Array.isArray(answerData)) {
            // Handle multiple choice - select multiple options
            answerData.forEach(index => {
                if (options[index]) {
                    options[index].classList.add('selected');
                }
            });
        } else if (!question.multipleChoice && typeof answerData === 'number') {
            // Handle single choice - select one option
            if (options[answerData]) {
                options[answerData].classList.add('selected');
            }
        }
    }

    showFeedback() {
        const question = this.questions[this.currentQuestionIndex];
        const userAnswer = this.userAnswers[this.currentQuestionIndex];

        if (userAnswer === undefined) return;

        let isCorrect = false;
        let userAnswerLetters = '';

        if (question.multipleChoice) {
            // Handle multiple choice questions
            if (Array.isArray(userAnswer)) {
                userAnswerLetters = userAnswer.map(index => question.options[index].charAt(0)).sort().join('');
                isCorrect = userAnswerLetters === question.correctAnswer;
            }
        } else {
            // Handle single choice questions
            userAnswerLetters = question.options[userAnswer].charAt(0);
            isCorrect = userAnswerLetters === question.correctAnswer;
        }

        // Update option styles
        document.querySelectorAll('.option').forEach((option, index) => {
            const optionLetter = option.querySelector('.option-letter').textContent;

            if (question.multipleChoice) {
                // For multiple choice questions
                if (Array.isArray(userAnswer) && userAnswer.includes(index)) {
                    // User selected this option
                    if (question.correctAnswer.includes(optionLetter)) {
                        // User selected a correct option
                        option.classList.add('correct');
                    } else {
                        // User selected an incorrect option
                        option.classList.add('incorrect');
                    }
                }
                // Don't mark unselected options as correct/incorrect
            } else {
                // For single choice questions
                if (index === userAnswer) {
                    // User selected this option
                    if (isCorrect) {
                        option.classList.add('correct');
                    } else {
                        option.classList.add('incorrect');
                    }
                }
                // Don't mark unselected options
            }
        });

        // Show feedback section
        const feedbackSection = document.getElementById('feedback-section');
        const feedbackResult = document.getElementById('feedback-result');
        const feedbackExplanation = document.getElementById('feedback-explanation');

        feedbackResult.textContent = isCorrect ? 'Correct!' : `Incorrect. The correct answer is ${question.correctAnswer}.`;
        feedbackResult.className = `feedback-result ${isCorrect ? 'correct' : 'incorrect'}`;

        if (question.explanation) {
            feedbackExplanation.textContent = question.explanation;
            feedbackExplanation.style.display = 'block';
        } else {
            feedbackExplanation.style.display = 'none';
        }

        feedbackSection.classList.remove('hidden');
    }

    submitAnswer() {
        const userAnswer = this.userAnswers[this.currentQuestionIndex];
        const question = this.questions[this.currentQuestionIndex];

        // Check if answer is provided
        if (userAnswer === undefined ||
            (question.multipleChoice && Array.isArray(userAnswer) && userAnswer.length === 0)) {
            this.showError('Please select an answer before submitting.');
            return;
        }

        // For multiple choice questions, check if user has selected the expected number of answers
        if (question.multipleChoice && Array.isArray(userAnswer)) {
            const expectedAnswerCount = this.getExpectedAnswerCount(question);
            if (userAnswer.length !== expectedAnswerCount) {
                const wordMap = {2: 'two', 3: 'three', 4: 'four', 5: 'five', 6: 'six'};
                const expectedWord = wordMap[expectedAnswerCount] || expectedAnswerCount.toString();
                this.showError(`Please select exactly ${expectedWord} answers for this question. You have selected ${userAnswer.length}.`);
                return;
            }
        }

        if (this.examMode === 'practice') {
            this.showFeedback();
        }

        // Move to next question or finish
        if (this.currentQuestionIndex < this.questions.length - 1) {
            this.navigateQuestion(1);
        } else {
            this.finishExam();
        }
    }

    navigateQuestion(direction) {
        const newIndex = this.currentQuestionIndex + direction;

        if (newIndex >= 0 && newIndex < this.questions.length) {
            this.currentQuestionIndex = newIndex;
            this.displayQuestion();
        }
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prev-question');
        const nextBtn = document.getElementById('next-question');
        const submitBtn = document.getElementById('submit-answer');

        // Previous button
        prevBtn.disabled = this.currentQuestionIndex === 0;

        // Next button
        const isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;
        nextBtn.style.display = isLastQuestion ? 'none' : 'inline-block';

        // Submit answer button is always hidden in this simplified navigation
        submitBtn.style.display = 'none';

        // Update exam controls visibility based on current question
        if (this.examMode === 'practice') {
            document.getElementById('finish-exam').style.display = isLastQuestion ? 'inline-block' : 'none';
            document.getElementById('submit-practice').style.display = 'inline-block';
        } else {
            document.getElementById('submit-exam').style.display = isLastQuestion ? 'inline-block' : 'none';
            document.getElementById('submit-practice').style.display = 'none';
        }
    }

    updateProgress() {
        const progress = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
        document.getElementById('progress-fill').style.width = `${progress}%`;

        // Update header progress
        document.getElementById('progress-display').textContent =
            `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;
    }

    updateScoreDisplay() {
        const answeredCount = Object.keys(this.userAnswers).length;
        const correctCount = this.calculateCorrectAnswers();

        document.getElementById('score-display').textContent =
            `Score: ${correctCount}/${answeredCount}`;
    }

    calculateCorrectAnswers() {
        let correct = 0;

        for (const [questionIndex, userAnswer] of Object.entries(this.userAnswers)) {
            const question = this.questions[parseInt(questionIndex)];

            if (question.multipleChoice) {
                // Handle multiple choice questions
                if (Array.isArray(userAnswer)) {
                    const userAnswerLetters = userAnswer.map(index => question.options[index].charAt(0)).sort().join('');
                    if (userAnswerLetters === question.correctAnswer) {
                        correct++;
                    }
                }
            } else {
                // Handle single choice questions
                const userAnswerLetter = question.options[userAnswer].charAt(0);
                if (userAnswerLetter === question.correctAnswer) {
                    correct++;
                }
            }
        }

        return correct;
    }

    submitExam() {
        // Show confirmation dialog for real exam mode
        if (this.examMode === 'exam') {
            const answeredCount = Object.keys(this.userAnswers).length;
            const totalQuestions = this.questions.length;
            const unansweredCount = totalQuestions - answeredCount;

            let confirmMessage = `Are you sure you want to submit your exam?\n\n`;
            confirmMessage += `Answered: ${answeredCount}/${totalQuestions} questions\n`;
            if (unansweredCount > 0) {
                confirmMessage += `Unanswered: ${unansweredCount} questions\n\n`;
                confirmMessage += `Unanswered questions will be marked as incorrect.`;
            }

            if (confirm(confirmMessage)) {
                this.finishExam();
            }
        } else {
            this.finishExam();
        }
    }

    finishExam() {
        this.isExamFinished = true;

        if (this.examMode === 'exam') {
            // Show score popup for real exam mode
            this.showScorePopup();
        } else {
            // Go directly to results for practice mode
            this.showResults();
        }
    }

    showResults() {
        const totalQuestions = this.questions.length;
        const answeredCount = Object.keys(this.userAnswers).length;
        const correctCount = this.calculateCorrectAnswers();
        const incorrectCount = answeredCount - correctCount;
        const unansweredCount = totalQuestions - answeredCount;
        const percentage = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 100) : 0;

        // Update score circle
        const scoreCircle = document.querySelector('.score-circle');
        const angle = (correctCount / totalQuestions) * 360;
        scoreCircle.style.background = `conic-gradient(#27ae60 0deg, #27ae60 ${angle}deg, #e9ecef ${angle}deg)`;

        // Update score displays
        document.getElementById('score-percentage').textContent = `${percentage}%`;
        document.getElementById('score-fraction').textContent = `${correctCount}/${totalQuestions}`;
        document.getElementById('correct-count').textContent = correctCount;
        document.getElementById('incorrect-count').textContent = incorrectCount;
        document.getElementById('unanswered-count').textContent = unansweredCount;

        // Update summary section
        document.getElementById('total-questions-summary').textContent = totalQuestions;
        document.getElementById('answered-questions-summary').textContent = answeredCount;
        document.getElementById('unanswered-questions-summary').textContent = unansweredCount;
        document.getElementById('correct-answers-summary').textContent = correctCount;
        document.getElementById('final-score-summary').textContent = `${percentage}%`;

        // Show/hide unanswered warning
        const unansweredWarning = document.getElementById('unanswered-warning');
        const unansweredCountWarning = document.getElementById('unanswered-count-warning');

        if (unansweredCount > 0) {
            unansweredCountWarning.textContent = unansweredCount;
            unansweredWarning.classList.remove('hidden');
        } else {
            unansweredWarning.classList.add('hidden');
        }

        // Keep home button visible
        document.getElementById('home-btn').classList.remove('hidden');

        this.showScreen('results-screen');
    }

    showScorePopup() {
        const totalQuestions = this.questions.length;
        const answeredCount = Object.keys(this.userAnswers).length;
        const correctCount = this.calculateCorrectAnswers();
        const incorrectCount = answeredCount - correctCount;
        const unansweredCount = totalQuestions - answeredCount;
        const percentage = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 100) : 0;

        // Update popup score circle
        const popupScoreCircle = document.querySelector('.popup-score-circle');
        const angle = (correctCount / totalQuestions) * 360;
        popupScoreCircle.style.background = `conic-gradient(#27ae60 0deg, #27ae60 ${angle}deg, #e9ecef ${angle}deg)`;

        // Update popup score displays
        document.getElementById('popup-score-percentage').textContent = `${percentage}%`;
        document.getElementById('popup-score-fraction').textContent = `${correctCount}/${totalQuestions}`;
        document.getElementById('popup-correct-count').textContent = correctCount;
        document.getElementById('popup-incorrect-count').textContent = incorrectCount;
        document.getElementById('popup-unanswered-count').textContent = unansweredCount;

        // Show the popup
        document.getElementById('score-popup').classList.remove('hidden');
    }

    showReview() {
        const reviewSection = document.getElementById('review-section');
        const reviewList = document.getElementById('review-list');

        reviewList.innerHTML = '';

        // Add summary header
        const summaryHeader = document.createElement('div');
        summaryHeader.className = 'review-summary';
        summaryHeader.innerHTML = `
            <h4>📋 Question Review Summary</h4>
            <p>Review your answers below. Incorrect and unanswered questions are highlighted for your attention.</p>
        `;
        reviewList.appendChild(summaryHeader);

        this.questions.forEach((question, index) => {
            const reviewItem = this.createReviewItem(question, index);
            reviewList.appendChild(reviewItem);
        });

        reviewSection.classList.remove('hidden');

        // Scroll to review section
        reviewSection.scrollIntoView({ behavior: 'smooth' });
    }

    createReviewItem(question, index) {
        const userAnswer = this.userAnswers[index];
        const div = document.createElement('div');
        div.className = 'review-item';

        let status = 'unanswered';
        let statusText = '⚪ Not answered';
        let answerText = '';
        let statusIcon = '⚪';

        if (userAnswer !== undefined) {
            let userAnswerText = '';
            let userAnswerLetters = '';
            let isCorrect = false;

            if (question.multipleChoice) {
                // Handle multiple choice questions
                if (Array.isArray(userAnswer)) {
                    userAnswerLetters = userAnswer.map(idx => question.options[idx].charAt(0)).sort().join('');
                    userAnswerText = userAnswer.map(idx => question.options[idx]).join(', ');
                    isCorrect = userAnswerLetters === question.correctAnswer;
                }
            } else {
                // Handle single choice questions
                userAnswerLetters = question.options[userAnswer].charAt(0);
                userAnswerText = question.options[userAnswer];
                isCorrect = userAnswerLetters === question.correctAnswer;
            }

            // Get correct answer text(s)
            const correctAnswerTexts = question.correctAnswer.split('').map(letter => {
                return question.options.find(option => option.charAt(0) === letter);
            }).filter(Boolean);

            status = isCorrect ? 'correct' : 'incorrect';
            statusText = isCorrect ? '✅ Correct' : '❌ Incorrect';
            statusIcon = isCorrect ? '✅' : '❌';
            answerText = `Your answer: ${userAnswerText} | Correct answer: ${correctAnswerTexts.join(', ')}`;
        } else {
            // Get correct answer text(s) for unanswered questions
            const correctAnswerTexts = question.correctAnswer.split('').map(letter => {
                return question.options.find(option => option.charAt(0) === letter);
            }).filter(Boolean);
            answerText = `Correct answer: ${correctAnswerTexts.join(', ')}`;
        }

        div.classList.add(status);
        div.innerHTML = `
            <div class="review-question-header">
                <span class="review-status-icon">${statusIcon}</span>
                <span class="review-question-number">Question ${index + 1}</span>
                <span class="review-status-text">${statusText}</span>
            </div>
            <div class="review-question-text">${question.text}</div>
            <div class="review-answer-details">${answerText}</div>
        `;

        return div;
    }

    restartExam() {
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.isExamFinished = false;

        // Reset questions to original state
        this.loadDefaultQuestions();

        // Reset displays
        document.getElementById('score-display').textContent = 'Score: 0/0';
        document.getElementById('review-section').classList.add('hidden');

        this.showScreen('start-screen');
    }

    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }

    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    showMessage(type, message) {
        // Simple message display - could be enhanced with a toast system
        console.log(`${type.toUpperCase()}: ${message}`);
    }

    showAnswers() {
        // Show home button
        document.getElementById('home-btn').classList.remove('hidden');

        // Generate answers list
        this.generateAnswersList();

        // Show answers screen
        this.showScreen('answers-screen');
    }

    generateAnswersList() {
        const answersList = document.getElementById('answers-list');
        answersList.innerHTML = '';

        this.questions.forEach((question, index) => {
            const answerItem = this.createAnswerItem(question, index);
            answersList.appendChild(answerItem);
        });
    }

    createAnswerItem(question, index) {
        const div = document.createElement('div');
        div.className = 'answer-item';
        div.dataset.questionIndex = index;

        const typeText = question.multipleChoice ? 'Multiple Choice' : 'Single Choice';
        const typeBadgeClass = question.multipleChoice ? 'multiple' : 'single';

        // Create options HTML with correct answer highlighted
        let optionsHTML = '';
        question.options.forEach((option, optIndex) => {
            const optionLetter = option.charAt(0);
            const isCorrect = question.correctAnswer.includes(optionLetter);
            const optionClass = isCorrect ? 'answer-option correct' : 'answer-option';
            const correctIcon = isCorrect ? '✅ ' : '';

            optionsHTML += `<div class="${optionClass}">${correctIcon}${option}</div>`;
        });

        div.innerHTML = `
            <div class="answer-type-badge ${typeBadgeClass}">${typeText}</div>
            <div class="answer-question">Question ${index + 1}: ${question.text}</div>
            <div class="answer-options">${optionsHTML}</div>
            <div class="answer-correct">✅ Correct Answer: ${question.correctAnswer}</div>
        `;

        return div;
    }

    resetPracticeControls() {
        // Reset button states
        document.getElementById('show-answer').classList.remove('hidden');
        document.getElementById('hide-answer').classList.add('hidden');

        // Remove any correct answer highlighting
        const options = document.querySelectorAll('.option');
        options.forEach(option => {
            option.classList.remove('correct-highlight');
        });

        // Hide practice controls only in exam mode
        if (this.examMode === 'exam') {
            document.getElementById('practice-controls').classList.add('hidden');
        }
    }

    showCorrectAnswer() {
        const question = this.questions[this.currentQuestionIndex];
        if (!question) return;

        // Find and highlight correct answer(s)
        const options = document.querySelectorAll('.option');
        options.forEach((option, index) => {
            const optionLetter = question.options[index].charAt(0);
            if (question.correctAnswer.includes(optionLetter)) {
                option.classList.add('correct-highlight');
            }
        });

        // Update button visibility
        document.getElementById('show-answer').classList.add('hidden');
        document.getElementById('hide-answer').classList.remove('hidden');
    }

    hideCorrectAnswer() {
        // Remove correct answer highlighting
        const options = document.querySelectorAll('.option');
        options.forEach(option => {
            option.classList.remove('correct-highlight');
        });

        // Update button visibility
        document.getElementById('show-answer').classList.remove('hidden');
        document.getElementById('hide-answer').classList.add('hidden');
    }

    showAllQuestions() {
        // Create a temporary results state to show review
        this.showResults();
        // Automatically show the review section
        setTimeout(() => {
            this.showReview();
        }, 100);
    }

    clearCurrentAnswer() {
        // Clear user's answer for current question
        delete this.userAnswers[this.currentQuestionIndex];

        // Remove all selections and feedback states
        const options = document.querySelectorAll('.option');
        options.forEach(option => {
            option.classList.remove('selected', 'correct', 'incorrect', 'correct-highlight');
        });

        // Hide feedback section
        document.getElementById('feedback-section').classList.add('hidden');

        // Hide correct answer highlighting
        this.hideCorrectAnswer();

        // Update displays
        this.updateNavigationButtons();
        this.updateScoreDisplay();
    }

    filterAnswers() {
        const searchTerm = document.getElementById('answer-search').value.toLowerCase();
        const filterType = document.getElementById('answer-filter').value;
        const answerItems = document.querySelectorAll('.answer-item');

        answerItems.forEach(item => {
            const questionIndex = parseInt(item.dataset.questionIndex);
            const question = this.questions[questionIndex];

            // Check search term
            const matchesSearch = searchTerm === '' ||
                question.text.toLowerCase().includes(searchTerm) ||
                question.options.some(option => option.toLowerCase().includes(searchTerm));

            // Check filter type
            const matchesFilter = filterType === 'all' ||
                (filterType === 'single' && !question.multipleChoice) ||
                (filterType === 'multiple' && question.multipleChoice);

            // Show/hide item
            if (matchesSearch && matchesFilter) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ExamApp();
});
